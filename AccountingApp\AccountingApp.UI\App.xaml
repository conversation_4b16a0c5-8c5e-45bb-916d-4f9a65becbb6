<Application x:Class="AccountingApp.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- Material Design Theme Configuration -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design Light Theme -->
                <materialDesign:BundledTheme BaseTheme="Light" 
                                           PrimaryColor="Blue" 
                                           SecondaryColor="Teal" />
                
                <!-- Material Design Default Styles -->
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Material Design Controls -->
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Button.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Card.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.DataGrid.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.TextBox.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.ComboBox.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.DatePicker.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.ListBox.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Menu.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.ProgressBar.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Snackbar.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.ToolTip.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Custom Application Styles -->
            <Style x:Key="NavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                <Setter Property="Height" Value="50"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="Margin" Value="0,2"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
            </Style>

            <Style x:Key="PageTitleStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Margin" Value="0,0,0,16"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            </Style>

            <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            </Style>

            <Style x:Key="DataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                <Setter Property="AutoGenerateColumns" Value="False"/>
                <Setter Property="CanUserAddRows" Value="False"/>
                <Setter Property="CanUserDeleteRows" Value="False"/>
                <Setter Property="IsReadOnly" Value="True"/>
                <Setter Property="SelectionMode" Value="Single"/>
                <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                <Setter Property="HeadersVisibility" Value="Column"/>
                <Setter Property="RowHeight" Value="40"/>
                <Setter Property="FontSize" Value="13"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
