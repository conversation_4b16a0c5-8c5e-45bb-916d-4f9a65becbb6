using System;
using System.Windows;
using AccountingApp.Core;

namespace AccountingApp.UI
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// Raises the <see cref="Application.Startup"/> event.
        /// </summary>
        /// <param name="e">A <see cref="StartupEventArgs"/> that contains the event data.</param>
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Initialize the database on application startup
            InitializeDatabase();
        }

        /// <summary>
        /// Initializes the database by ensuring it exists and is properly configured.
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                using (var context = new AppDbContext())
                {
                    // This will create the database if it doesn't exist
                    context.Database.Initialize(force: false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to initialize database: {ex.Message}", 
                              "Database Error", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Error);
                
                // Exit the application if database initialization fails
                Current.Shutdown();
            }
        }

        /// <summary>
        /// Raises the <see cref="Application.Exit"/> event.
        /// </summary>
        /// <param name="e">An <see cref="ExitEventArgs"/> that contains the event data.</param>
        protected override void OnExit(ExitEventArgs e)
        {
            // Perform any cleanup operations here
            base.OnExit(e);
        }
    }
}
