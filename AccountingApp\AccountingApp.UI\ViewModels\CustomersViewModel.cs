using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using AccountingApp.Core;
using AccountingApp.Data.Repositories;
using AccountingApp.UI.Commands;
using AccountingApp.UI.Views.Dialogs;

namespace AccountingApp.UI.ViewModels
{
    /// <summary>
    /// ViewModel for the Customers page.
    /// </summary>
    public class CustomersViewModel : BaseViewModel
    {
        private readonly Repository<Customer> _customerRepository;
        private Customer _selectedCustomer;

        /// <summary>
        /// Initializes a new instance of the <see cref="CustomersViewModel"/> class.
        /// </summary>
        public CustomersViewModel()
        {
            var context = new AppDbContext();
            _customerRepository = new Repository<Customer>(context);
            
            Customers = new ObservableCollection<Customer>();
            
            // Initialize commands
            AddCustomerCommand = new RelayCommand(AddCustomer);
            EditCustomerCommand = new RelayCommand(EditCustomer, CanEditOrDeleteCustomer);
            DeleteCustomerCommand = new RelayCommand(DeleteCustomer, CanEditOrDeleteCustomer);
            
            // Load customers
            LoadCustomers();
        }

        /// <summary>
        /// Gets the collection of customers.
        /// </summary>
        public ObservableCollection<Customer> Customers { get; }

        /// <summary>
        /// Gets or sets the selected customer.
        /// </summary>
        public Customer SelectedCustomer
        {
            get => _selectedCustomer;
            set => SetProperty(ref _selectedCustomer, value);
        }

        /// <summary>
        /// Gets the command to add a new customer.
        /// </summary>
        public ICommand AddCustomerCommand { get; }

        /// <summary>
        /// Gets the command to edit the selected customer.
        /// </summary>
        public ICommand EditCustomerCommand { get; }

        /// <summary>
        /// Gets the command to delete the selected customer.
        /// </summary>
        public ICommand DeleteCustomerCommand { get; }

        /// <summary>
        /// Loads all customers from the database.
        /// </summary>
        private void LoadCustomers()
        {
            try
            {
                var customers = _customerRepository.GetAll().OrderBy(c => c.Name);
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading customers: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Adds a new customer.
        /// </summary>
        private void AddCustomer()
        {
            var dialog = new CustomerDialog();
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    var customer = dialog.Customer;
                    _customerRepository.Add(customer);
                    _customerRepository.SaveChanges();
                    Customers.Add(customer);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error adding customer: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Edits the selected customer.
        /// </summary>
        private void EditCustomer()
        {
            if (SelectedCustomer == null) return;

            var dialog = new CustomerDialog(SelectedCustomer);
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    _customerRepository.Update(dialog.Customer);
                    _customerRepository.SaveChanges();
                    LoadCustomers(); // Refresh the list
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error updating customer: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Deletes the selected customer.
        /// </summary>
        private void DeleteCustomer()
        {
            if (SelectedCustomer == null) return;

            var result = MessageBox.Show($"Are you sure you want to delete customer '{SelectedCustomer.Name}'?", 
                                       "Confirm Delete", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _customerRepository.Remove(SelectedCustomer);
                    _customerRepository.SaveChanges();
                    Customers.Remove(SelectedCustomer);
                    SelectedCustomer = null;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting customer: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Determines whether the edit or delete customer commands can execute.
        /// </summary>
        /// <returns>True if a customer is selected; otherwise, false.</returns>
        private bool CanEditOrDeleteCustomer()
        {
            return SelectedCustomer != null;
        }
    }
}
