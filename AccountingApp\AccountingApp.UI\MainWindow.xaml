<Window x:Class="AccountingApp.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Mobile Phone Repair Shop - Accounting System" 
        Height="800" 
        Width="1200"
        MinHeight="600"
        MinWidth="900"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.ColumnDefinitions>
                <!-- Navigation Panel -->
                <ColumnDefinition Width="250"/>
                <!-- Main Content Area -->
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Navigation Panel -->
            <materialDesign:Card Grid.Column="0" 
                               materialDesign:ShadowAssist.ShadowDepth="Depth2"
                               Margin="0"
                               CornerRadius="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <!-- Header -->
                        <RowDefinition Height="Auto"/>
                        <!-- Navigation Items -->
                        <RowDefinition Height="*"/>
                        <!-- Footer -->
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Application Header -->
                    <StackPanel Grid.Row="0" 
                              Background="{DynamicResource PrimaryHueMidBrush}"
                              Margin="0,0,0,8">
                        <materialDesign:PackIcon Kind="Cellphone" 
                                               Width="48" 
                                               Height="48" 
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               Margin="0,16,0,8"/>
                        <TextBlock Text="Phone Repair Shop" 
                                 Foreground="White"
                                 FontSize="16"
                                 FontWeight="Bold"
                                 HorizontalAlignment="Center"
                                 Margin="0,0,0,16"/>
                    </StackPanel>

                    <!-- Navigation Menu -->
                    <StackPanel Grid.Row="1" Margin="8">
                        <Button x:Name="CustomersButton"
                              Style="{StaticResource NavigationButtonStyle}"
                              Click="NavigationButton_Click"
                              Tag="Customers">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Account" 
                                                       Width="20" 
                                                       Height="20" 
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="Customers"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="DevicesButton"
                              Style="{StaticResource NavigationButtonStyle}"
                              Click="NavigationButton_Click"
                              Tag="Devices">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cellphone" 
                                                       Width="20" 
                                                       Height="20" 
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="Devices"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="AccessoriesButton"
                              Style="{StaticResource NavigationButtonStyle}"
                              Click="NavigationButton_Click"
                              Tag="Accessories">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Shopping" 
                                                       Width="20" 
                                                       Height="20" 
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="Accessories"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InvoicesButton"
                              Style="{StaticResource NavigationButtonStyle}"
                              Click="NavigationButton_Click"
                              Tag="Invoices">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Receipt" 
                                                       Width="20" 
                                                       Height="20" 
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="Invoices"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- Footer -->
                    <StackPanel Grid.Row="2" Margin="8">
                        <Separator Margin="0,8"/>
                        <TextBlock Text="© 2025 Phone Repair Shop" 
                                 FontSize="10"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 HorizontalAlignment="Center"
                                 Margin="0,8"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Main Content Area -->
            <Grid Grid.Column="1" Margin="16">
                <Frame x:Name="MainFrame" 
                     NavigationUIVisibility="Hidden"
                     Background="Transparent"/>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</Window>
