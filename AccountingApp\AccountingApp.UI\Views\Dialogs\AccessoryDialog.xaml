<Window x:Class="AccountingApp.UI.Views.Dialogs.AccessoryDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Accessory Details" 
        Height="600" 
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Dialog Title -->
        <TextBlock Grid.Row="0" 
                   x:Name="DialogTitle"
                   Text="Add Accessory" 
                   Style="{StaticResource PageTitleStyle}"
                   Margin="0,0,0,24"/>

        <!-- Accessory Form -->
        <materialDesign:Card Grid.Row="1" 
                           Style="{StaticResource CardStyle}"
                           Margin="0">
            <StackPanel>
                <!-- Name Field -->
                <TextBox x:Name="NameTextBox"
                         materialDesign:HintAssist.Hint="Accessory Name"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"
                         MaxLength="100"/>

                <!-- Category Field -->
                <ComboBox x:Name="CategoryComboBox"
                          materialDesign:HintAssist.Hint="Category"
                          Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                          IsEditable="True"
                          Margin="0,0,0,16">
                    <ComboBoxItem Content="Case"/>
                    <ComboBoxItem Content="Screen Protector"/>
                    <ComboBoxItem Content="Charger"/>
                    <ComboBoxItem Content="Cable"/>
                    <ComboBoxItem Content="Headphones"/>
                    <ComboBoxItem Content="Power Bank"/>
                    <ComboBoxItem Content="Car Mount"/>
                    <ComboBoxItem Content="Wireless Charger"/>
                    <ComboBoxItem Content="Memory Card"/>
                    <ComboBoxItem Content="Adapter"/>
                    <ComboBoxItem Content="Other"/>
                </ComboBox>

                <!-- Description Field -->
                <TextBox x:Name="DescriptionTextBox"
                         materialDesign:HintAssist.Hint="Description"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinLines="3"
                         MaxLines="5"
                         VerticalScrollBarVisibility="Auto"
                         MaxLength="500"
                         Margin="0,0,0,16"/>

                <!-- Price Field -->
                <TextBox x:Name="PriceTextBox"
                         materialDesign:HintAssist.Hint="Price"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"/>

                <!-- Stock Quantity Field -->
                <TextBox x:Name="StockQuantityTextBox"
                         materialDesign:HintAssist.Hint="Stock Quantity"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"/>

                <!-- Added Date Field -->
                <DatePicker x:Name="AddedDatePicker"
                            materialDesign:HintAssist.Hint="Added Date"
                            Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                            Margin="0,0,0,16"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right"
                    Margin="0,24,0,0">
            <Button x:Name="CancelButton"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Content="Cancel"
                    Click="CancelButton_Click"
                    Margin="0,0,8,0"
                    Width="80"/>

            <Button x:Name="SaveButton"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Content="Save"
                    Click="SaveButton_Click"
                    Width="80"/>
        </StackPanel>
    </Grid>
</Window>
