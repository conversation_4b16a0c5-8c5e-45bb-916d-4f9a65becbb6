using System;
using System.Windows;
using AccountingApp.Core;

namespace AccountingApp.UI.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for CustomerDialog.xaml
    /// </summary>
    public partial class CustomerDialog : Window
    {
        /// <summary>
        /// Gets the customer being edited or created.
        /// </summary>
        public Customer Customer { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="CustomerDialog"/> class for adding a new customer.
        /// </summary>
        public CustomerDialog()
        {
            InitializeComponent();
            Customer = new Customer();
            DialogTitle.Text = "Add Customer";
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CustomerDialog"/> class for editing an existing customer.
        /// </summary>
        /// <param name="customer">The customer to edit.</param>
        public CustomerDialog(Customer customer)
        {
            InitializeComponent();
            
            if (customer == null)
                throw new ArgumentNullException(nameof(customer));

            // Create a copy of the customer to avoid modifying the original until save
            Customer = new Customer
            {
                Id = customer.Id,
                Name = customer.Name,
                PhoneNumber = customer.PhoneNumber,
                Email = customer.Email,
                Address = customer.Address,
                CreatedAt = customer.CreatedAt
            };

            DialogTitle.Text = "Edit Customer";
            LoadCustomerData();
        }

        /// <summary>
        /// Loads the customer data into the form fields.
        /// </summary>
        private void LoadCustomerData()
        {
            NameTextBox.Text = Customer.Name;
            PhoneTextBox.Text = Customer.PhoneNumber;
            EmailTextBox.Text = Customer.Email;
            AddressTextBox.Text = Customer.Address;
        }

        /// <summary>
        /// Validates the form data.
        /// </summary>
        /// <returns>True if the form is valid; otherwise, false.</returns>
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("Please enter a customer name.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            // Validate email format if provided
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(EmailTextBox.Text);
                    if (addr.Address != EmailTextBox.Text)
                    {
                        MessageBox.Show("Please enter a valid email address.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        EmailTextBox.Focus();
                        return false;
                    }
                }
                catch
                {
                    MessageBox.Show("Please enter a valid email address.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    EmailTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Saves the form data to the customer object.
        /// </summary>
        private void SaveFormData()
        {
            Customer.Name = NameTextBox.Text.Trim();
            Customer.PhoneNumber = PhoneTextBox.Text.Trim();
            Customer.Email = EmailTextBox.Text.Trim();
            Customer.Address = AddressTextBox.Text.Trim();

            // Set created date for new customers
            if (Customer.Id == 0)
            {
                Customer.CreatedAt = DateTime.Now;
            }
        }

        /// <summary>
        /// Handles the click event for the Save button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                SaveFormData();
                DialogResult = true;
                Close();
            }
        }

        /// <summary>
        /// Handles the click event for the Cancel button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
