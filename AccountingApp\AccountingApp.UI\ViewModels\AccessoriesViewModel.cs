using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using AccountingApp.Core;
using AccountingApp.Data.Repositories;
using AccountingApp.UI.Commands;
using AccountingApp.UI.Views.Dialogs;

namespace AccountingApp.UI.ViewModels
{
    /// <summary>
    /// ViewModel for the Accessories page.
    /// </summary>
    public class AccessoriesViewModel : BaseViewModel
    {
        private readonly Repository<Accessory> _accessoryRepository;
        private Accessory _selectedAccessory;

        /// <summary>
        /// Initializes a new instance of the <see cref="AccessoriesViewModel"/> class.
        /// </summary>
        public AccessoriesViewModel()
        {
            var context = new AppDbContext();
            _accessoryRepository = new Repository<Accessory>(context);
            
            Accessories = new ObservableCollection<Accessory>();
            
            // Initialize commands
            AddAccessoryCommand = new RelayCommand(AddAccessory);
            EditAccessoryCommand = new RelayCommand(EditAccessory, CanEditOrDeleteAccessory);
            DeleteAccessoryCommand = new RelayCommand(DeleteAccessory, CanEditOrDeleteAccessory);
            
            // Load accessories
            LoadAccessories();
        }

        /// <summary>
        /// Gets the collection of accessories.
        /// </summary>
        public ObservableCollection<Accessory> Accessories { get; }

        /// <summary>
        /// Gets or sets the selected accessory.
        /// </summary>
        public Accessory SelectedAccessory
        {
            get => _selectedAccessory;
            set => SetProperty(ref _selectedAccessory, value);
        }

        /// <summary>
        /// Gets the command to add a new accessory.
        /// </summary>
        public ICommand AddAccessoryCommand { get; }

        /// <summary>
        /// Gets the command to edit the selected accessory.
        /// </summary>
        public ICommand EditAccessoryCommand { get; }

        /// <summary>
        /// Gets the command to delete the selected accessory.
        /// </summary>
        public ICommand DeleteAccessoryCommand { get; }

        /// <summary>
        /// Loads all accessories from the database.
        /// </summary>
        private void LoadAccessories()
        {
            try
            {
                var accessories = _accessoryRepository.GetAll().OrderBy(a => a.Name);
                Accessories.Clear();
                foreach (var accessory in accessories)
                {
                    Accessories.Add(accessory);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading accessories: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Adds a new accessory.
        /// </summary>
        private void AddAccessory()
        {
            var dialog = new AccessoryDialog();
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    var accessory = dialog.Accessory;
                    _accessoryRepository.Add(accessory);
                    _accessoryRepository.SaveChanges();
                    Accessories.Add(accessory);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error adding accessory: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Edits the selected accessory.
        /// </summary>
        private void EditAccessory()
        {
            if (SelectedAccessory == null) return;

            var dialog = new AccessoryDialog(SelectedAccessory);
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    _accessoryRepository.Update(dialog.Accessory);
                    _accessoryRepository.SaveChanges();
                    LoadAccessories(); // Refresh the list
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error updating accessory: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Deletes the selected accessory.
        /// </summary>
        private void DeleteAccessory()
        {
            if (SelectedAccessory == null) return;

            var result = MessageBox.Show($"Are you sure you want to delete accessory '{SelectedAccessory.Name}'?", 
                                       "Confirm Delete", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _accessoryRepository.Remove(SelectedAccessory);
                    _accessoryRepository.SaveChanges();
                    Accessories.Remove(SelectedAccessory);
                    SelectedAccessory = null;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting accessory: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Determines whether the edit or delete accessory commands can execute.
        /// </summary>
        /// <returns>True if an accessory is selected; otherwise, false.</returns>
        private bool CanEditOrDeleteAccessory()
        {
            return SelectedAccessory != null;
        }
    }
}
