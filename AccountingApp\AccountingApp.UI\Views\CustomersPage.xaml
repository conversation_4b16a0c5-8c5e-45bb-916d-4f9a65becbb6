<Page x:Class="AccountingApp.UI.Views.CustomersPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Customers">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" 
                   Text="Customers" 
                   Style="{StaticResource PageTitleStyle}"/>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="1" 
                    Orientation="Horizontal" 
                    Margin="0,0,0,16">
            <Button x:Name="AddCustomerButton"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Content="Add Customer"
                    Click="AddCustomerButton_Click"
                    Margin="0,0,8,0">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Plus"/>
                </Button.CommandParameter>
            </Button>

            <Button x:Name="EditCustomerButton"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Content="Edit Customer"
                    Click="EditCustomerButton_Click"
                    IsEnabled="{Binding SelectedCustomer, Converter={x:Static materialDesign:NotNullToBooleanConverter.Instance}}"
                    Margin="0,0,8,0">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Edit"/>
                </Button.CommandParameter>
            </Button>

            <Button x:Name="DeleteCustomerButton"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Content="Delete Customer"
                    Click="DeleteCustomerButton_Click"
                    IsEnabled="{Binding SelectedCustomer, Converter={x:Static materialDesign:NotNullToBooleanConverter.Instance}}"
                    Foreground="{DynamicResource ValidationErrorBrush}">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Delete"/>
                </Button.CommandParameter>
            </Button>
        </StackPanel>

        <!-- Customers DataGrid -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
            <DataGrid x:Name="CustomersDataGrid"
                      Style="{StaticResource DataGridStyle}"
                      ItemsSource="{Binding Customers}"
                      SelectedItem="{Binding SelectedCustomer}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" 
                                        Binding="{Binding Id}" 
                                        Width="60"/>
                    <DataGridTextColumn Header="Name" 
                                        Binding="{Binding Name}" 
                                        Width="200"/>
                    <DataGridTextColumn Header="Phone Number" 
                                        Binding="{Binding PhoneNumber}" 
                                        Width="150"/>
                    <DataGridTextColumn Header="Email" 
                                        Binding="{Binding Email}" 
                                        Width="200"/>
                    <DataGridTextColumn Header="Address" 
                                        Binding="{Binding Address}" 
                                        Width="*"/>
                    <DataGridTextColumn Header="Created At" 
                                        Binding="{Binding CreatedAt, StringFormat='{}{0:MM/dd/yyyy}'}" 
                                        Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
