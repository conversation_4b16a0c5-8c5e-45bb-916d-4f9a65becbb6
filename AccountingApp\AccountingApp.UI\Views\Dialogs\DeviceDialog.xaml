<Window x:Class="AccountingApp.UI.Views.Dialogs.DeviceDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Device Details" 
        Height="650" 
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Dialog Title -->
        <TextBlock Grid.Row="0" 
                   x:Name="DialogTitle"
                   Text="Add Device" 
                   Style="{StaticResource PageTitleStyle}"
                   Margin="0,0,0,24"/>

        <!-- Device Form -->
        <materialDesign:Card Grid.Row="1" 
                           Style="{StaticResource CardStyle}"
                           Margin="0">
            <StackPanel>
                <!-- Brand Field -->
                <TextBox x:Name="BrandTextBox"
                         materialDesign:HintAssist.Hint="Brand (e.g., Apple, Samsung)"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"
                         MaxLength="50"/>

                <!-- Model Field -->
                <TextBox x:Name="ModelTextBox"
                         materialDesign:HintAssist.Hint="Model (e.g., iPhone 13, Galaxy S21)"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"
                         MaxLength="100"/>

                <!-- Serial Number Field -->
                <TextBox x:Name="SerialNumberTextBox"
                         materialDesign:HintAssist.Hint="Serial Number"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"
                         MaxLength="50"/>

                <!-- Problem Description Field -->
                <TextBox x:Name="ProblemTextBox"
                         materialDesign:HintAssist.Hint="Problem Description"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinLines="3"
                         MaxLines="5"
                         VerticalScrollBarVisibility="Auto"
                         MaxLength="500"
                         Margin="0,0,0,16"/>

                <!-- Status Field -->
                <ComboBox x:Name="StatusComboBox"
                          materialDesign:HintAssist.Hint="Status"
                          Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                          Margin="0,0,0,16">
                    <ComboBoxItem Content="Received"/>
                    <ComboBoxItem Content="Diagnosing"/>
                    <ComboBoxItem Content="Waiting for Parts"/>
                    <ComboBoxItem Content="In Repair"/>
                    <ComboBoxItem Content="Testing"/>
                    <ComboBoxItem Content="Completed"/>
                    <ComboBoxItem Content="Ready for Pickup"/>
                    <ComboBoxItem Content="Delivered"/>
                    <ComboBoxItem Content="Cannot Repair"/>
                </ComboBox>

                <!-- Estimated Cost Field -->
                <TextBox x:Name="EstimatedCostTextBox"
                         materialDesign:HintAssist.Hint="Estimated Cost"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"/>

                <!-- Actual Cost Field -->
                <TextBox x:Name="ActualCostTextBox"
                         materialDesign:HintAssist.Hint="Actual Cost"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"/>

                <!-- Received Date Field -->
                <DatePicker x:Name="ReceivedDatePicker"
                            materialDesign:HintAssist.Hint="Received Date"
                            Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                            Margin="0,0,0,16"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right"
                    Margin="0,24,0,0">
            <Button x:Name="CancelButton"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Content="Cancel"
                    Click="CancelButton_Click"
                    Margin="0,0,8,0"
                    Width="80"/>

            <Button x:Name="SaveButton"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Content="Save"
                    Click="SaveButton_Click"
                    Width="80"/>
        </StackPanel>
    </Grid>
</Window>
