<Page x:Class="AccountingApp.UI.Views.AccessoriesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Accessories">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" 
                   Text="Accessories" 
                   Style="{StaticResource PageTitleStyle}"/>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="1" 
                    Orientation="Horizontal" 
                    Margin="0,0,0,16">
            <Button x:Name="AddAccessoryButton"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Content="Add Accessory"
                    Click="AddAccessoryButton_Click"
                    Margin="0,0,8,0">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Plus"/>
                </Button.CommandParameter>
            </Button>

            <Button x:Name="EditAccessoryButton"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Content="Edit Accessory"
                    Click="EditAccessoryButton_Click"
                    IsEnabled="{Binding SelectedAccessory, Converter={x:Static materialDesign:NotNullToBooleanConverter.Instance}}"
                    Margin="0,0,8,0">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Edit"/>
                </Button.CommandParameter>
            </Button>

            <Button x:Name="DeleteAccessoryButton"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Content="Delete Accessory"
                    Click="DeleteAccessoryButton_Click"
                    IsEnabled="{Binding SelectedAccessory, Converter={x:Static materialDesign:NotNullToBooleanConverter.Instance}}"
                    Foreground="{DynamicResource ValidationErrorBrush}">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Delete"/>
                </Button.CommandParameter>
            </Button>
        </StackPanel>

        <!-- Accessories DataGrid -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
            <DataGrid x:Name="AccessoriesDataGrid"
                      Style="{StaticResource DataGridStyle}"
                      ItemsSource="{Binding Accessories}"
                      SelectedItem="{Binding SelectedAccessory}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" 
                                        Binding="{Binding Id}" 
                                        Width="60"/>
                    <DataGridTextColumn Header="Name" 
                                        Binding="{Binding Name}" 
                                        Width="200"/>
                    <DataGridTextColumn Header="Category" 
                                        Binding="{Binding Category}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="Description" 
                                        Binding="{Binding Description}" 
                                        Width="250"/>
                    <DataGridTextColumn Header="Price" 
                                        Binding="{Binding Price, StringFormat='{}{0:C}'}" 
                                        Width="100"/>
                    <DataGridTextColumn Header="Stock" 
                                        Binding="{Binding StockQuantity}" 
                                        Width="80"/>
                    <DataGridTextColumn Header="Added Date" 
                                        Binding="{Binding AddedDate, StringFormat='{}{0:MM/dd/yyyy}'}" 
                                        Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
