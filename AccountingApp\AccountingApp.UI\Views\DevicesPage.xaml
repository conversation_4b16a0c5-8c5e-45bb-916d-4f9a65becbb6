<Page x:Class="AccountingApp.UI.Views.DevicesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Devices">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" 
                   Text="Devices" 
                   Style="{StaticResource PageTitleStyle}"/>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="1" 
                    Orientation="Horizontal" 
                    Margin="0,0,0,16">
            <Button x:Name="AddDeviceButton"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Content="Add Device"
                    Click="AddDeviceButton_Click"
                    Margin="0,0,8,0">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Plus"/>
                </Button.CommandParameter>
            </Button>

            <Button x:Name="EditDeviceButton"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Content="Edit Device"
                    Click="EditDeviceButton_Click"
                    IsEnabled="{Binding SelectedDevice, Converter={x:Static materialDesign:NotNullToBooleanConverter.Instance}}"
                    Margin="0,0,8,0">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Edit"/>
                </Button.CommandParameter>
            </Button>

            <Button x:Name="DeleteDeviceButton"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Content="Delete Device"
                    Click="DeleteDeviceButton_Click"
                    IsEnabled="{Binding SelectedDevice, Converter={x:Static materialDesign:NotNullToBooleanConverter.Instance}}"
                    Foreground="{DynamicResource ValidationErrorBrush}">
                <Button.CommandParameter>
                    <materialDesign:PackIcon Kind="Delete"/>
                </Button.CommandParameter>
            </Button>
        </StackPanel>

        <!-- Devices DataGrid -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
            <DataGrid x:Name="DevicesDataGrid"
                      Style="{StaticResource DataGridStyle}"
                      ItemsSource="{Binding Devices}"
                      SelectedItem="{Binding SelectedDevice}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" 
                                        Binding="{Binding Id}" 
                                        Width="60"/>
                    <DataGridTextColumn Header="Brand" 
                                        Binding="{Binding Brand}" 
                                        Width="100"/>
                    <DataGridTextColumn Header="Model" 
                                        Binding="{Binding Model}" 
                                        Width="150"/>
                    <DataGridTextColumn Header="Serial Number" 
                                        Binding="{Binding SerialNumber}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="Problem" 
                                        Binding="{Binding Problem}" 
                                        Width="200"/>
                    <DataGridTextColumn Header="Status" 
                                        Binding="{Binding Status}" 
                                        Width="100"/>
                    <DataGridTextColumn Header="Estimated Cost" 
                                        Binding="{Binding EstimatedCost, StringFormat='{}{0:C}'}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="Actual Cost" 
                                        Binding="{Binding ActualCost, StringFormat='{}{0:C}'}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="Received Date" 
                                        Binding="{Binding ReceivedDate, StringFormat='{}{0:MM/dd/yyyy}'}" 
                                        Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
