# Deployment Guide - Mobile Phone Repair Shop Application

This guide provides detailed instructions for packaging and deploying the AccountingApp to target Windows systems (Windows 7 and later).

## 🎯 Deployment Overview

### Target Requirements
- **Operating System**: Windows 7, 8, 8.1, 10, 11
- **.NET Framework**: 4.7.2 or higher
- **Architecture**: x86, x64, or AnyCPU
- **Permissions**: Standard user (no admin required for normal operation)

### Deployment Methods
1. **ClickOnce Deployment** (Recommended for easy updates)
2. **WiX Toolset MSI** (Professional installer)
3. **Portable Application** (Xcopy deployment)

## 📦 Method 1: ClickOnce Deployment

### Advantages
- Automatic updates
- Easy installation for end users
- Built-in security and sandboxing
- No administrator privileges required

### Setup Steps

#### 1. Configure Project Properties
In Visual Studio, right-click `AccountingApp.UI` project → Properties:

```xml
<!-- Application Tab -->
Assembly name: AccountingApp
Default namespace: AccountingApp.UI
Target framework: .NET Framework 4.7.2
Output type: Windows Application
Startup object: AccountingApp.UI.App

<!-- Publish Tab -->
Publishing folder location: \\server\share\AccountingApp\
Installation folder URL: http://yourserver.com/AccountingApp/
Install mode: Online only / Available offline
```

#### 2. Set Application Information
```xml
Product name: Mobile Phone Repair Shop
Publisher name: Your Company Name
Support URL: http://yourcompany.com/support
Description: Complete accounting solution for mobile phone repair shops
```

#### 3. Configure Prerequisites
Check required components:
- [x] .NET Framework 4.7.2
- [x] Windows Installer 4.5
- [x] SQL Server Compact 4.0

#### 4. Application Files Settings
```
AccountingApp.UI.exe - Include (Required)
AccountingApp.Core.dll - Include (Required)
AccountingApp.Data.dll - Include (Required)
EntityFramework.dll - Include (Required)
MaterialDesignThemes.Wpf.dll - Include (Required)
System.Data.SQLite.dll - Include (Required)
```

#### 5. Publish Application
```bash
# Command line publishing
msbuild AccountingApp.UI.csproj /p:PublishProfile=ClickOnce /p:PublishUrl="\\server\share\"
```

## 🔧 Method 2: WiX Toolset MSI Installer

### Prerequisites
1. Install WiX Toolset v3.11 or later
2. Install WiX Visual Studio Extension

### Create WiX Setup Project

#### 1. Add WiX Project to Solution
```xml
<!-- File: Setup.wixproj -->
<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>3.11</ProductVersion>
    <ProjectGuid>{********-1234-1234-1234-************}</ProjectGuid>
    <SchemaVersion>2.0</SchemaVersion>
    <OutputName>AccountingAppSetup</OutputName>
    <OutputType>Package</OutputType>
  </PropertyGroup>
</Project>
```

#### 2. Create Product.wxs
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" 
           Name="Mobile Phone Repair Shop" 
           Language="1033" 
           Version="*******" 
           Manufacturer="Your Company" 
           UpgradeCode="{********-1234-1234-1234-************}">
    
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine" 
             Description="Mobile Phone Repair Shop Accounting Application" />

    <MajorUpgrade DowngradeErrorMessage="A newer version is already installed." />
    <MediaTemplate EmbedCab="yes" />

    <!-- .NET Framework 4.7.2 Prerequisite -->
    <PropertyRef Id="WIX_IS_NETFRAMEWORK_472_OR_LATER_INSTALLED"/>
    <Condition Message="This application requires .NET Framework 4.7.2 or later.">
      <![CDATA[Installed OR WIX_IS_NETFRAMEWORK_472_OR_LATER_INSTALLED]]>
    </Condition>

    <!-- Application Files -->
    <Feature Id="ProductFeature" Title="AccountingApp" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>
  </Product>

  <Fragment>
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="AccountingApp" />
      </Directory>
    </Directory>
  </Fragment>

  <Fragment>
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="AccountingApp.UI.exe">
        <File Source="$(var.AccountingApp.UI.TargetPath)" />
      </Component>
      <Component Id="AccountingApp.Core.dll">
        <File Source="$(var.AccountingApp.Core.TargetPath)" />
      </Component>
      <Component Id="AccountingApp.Data.dll">
        <File Source="$(var.AccountingApp.Data.TargetPath)" />
      </Component>
      <!-- Add other required DLLs -->
    </ComponentGroup>
  </Fragment>
</Wix>
```

#### 3. Build MSI Package
```bash
# Command line build
candle Product.wxs
light Product.wixobj -out AccountingAppSetup.msi
```

## 📁 Method 3: Portable Application

### Advantages
- No installation required
- Can run from USB drive
- Easy to distribute
- No registry modifications

### Create Portable Package

#### 1. Build Release Configuration
```bash
# Build in Release mode
msbuild AccountingApp.sln /p:Configuration=Release /p:Platform="Any CPU"
```

#### 2. Gather Required Files
Create folder structure:
```
AccountingApp-Portable/
├── AccountingApp.UI.exe
├── AccountingApp.Core.dll
├── AccountingApp.Data.dll
├── EntityFramework.dll
├── EntityFramework.SqlServer.dll
├── MaterialDesignThemes.Wpf.dll
├── MaterialDesignColors.dll
├── System.Data.SQLite.dll
├── System.Data.SQLite.EF6.dll
├── x86/
│   └── SQLite.Interop.dll
├── x64/
│   └── SQLite.Interop.dll
├── App.config
└── README.txt
```

#### 3. Create Launcher Script
```batch
@echo off
REM AccountingApp Launcher
REM Check for .NET Framework 4.7.2

echo Starting Mobile Phone Repair Shop Application...

REM Set data directory to application folder
set APPDATA=%~dp0Data

REM Create data directory if it doesn't exist
if not exist "%APPDATA%" mkdir "%APPDATA%"

REM Launch application
start "" "%~dp0AccountingApp.UI.exe"
```

#### 4. Package for Distribution
```bash
# Create ZIP archive
7z a AccountingApp-v1.0-Portable.zip AccountingApp-Portable\*

# Or create self-extracting archive
7z a -sfx AccountingApp-v1.0-Portable.exe AccountingApp-Portable\*
```

## 🔐 Code Signing (Optional but Recommended)

### Obtain Code Signing Certificate
1. Purchase from trusted CA (DigiCert, Sectigo, etc.)
2. Or create self-signed for internal use

### Sign Executables
```bash
# Using SignTool.exe
signtool sign /f "certificate.pfx" /p "password" /t "http://timestamp.digicert.com" AccountingApp.UI.exe

# For ClickOnce
mage -Sign AccountingApp.application -CertFile certificate.pfx -Password password
```

## 📋 Pre-Deployment Checklist

### Application Testing
- [ ] Test on clean Windows 7 machine
- [ ] Test on Windows 10/11
- [ ] Verify database creation on first run
- [ ] Test all CRUD operations
- [ ] Verify Material Design themes load correctly
- [ ] Test invoice generation and calculations

### File Verification
- [ ] All required DLLs included
- [ ] SQLite native libraries (x86/x64) present
- [ ] App.config with correct connection string
- [ ] Material Design resource files included

### Documentation
- [ ] User manual created
- [ ] Installation instructions
- [ ] System requirements documented
- [ ] Troubleshooting guide prepared

## 🚀 Distribution Strategies

### Internal Distribution
- Network share deployment
- Group Policy software installation
- SCCM/Intune deployment

### External Distribution
- Company website download
- Software distribution platforms
- Direct customer delivery

### Update Management
- ClickOnce automatic updates
- Manual update notifications
- Version checking mechanism

## 🔧 Post-Deployment Support

### Common Installation Issues
1. **Missing .NET Framework**: Provide download link
2. **SQLite Provider Issues**: Include all native libraries
3. **Permission Problems**: Run installer as administrator

### Monitoring and Maintenance
- Application usage analytics
- Error reporting mechanism
- Regular security updates
- Database backup procedures

### User Training
- Application overview training
- Feature-specific tutorials
- Best practices documentation
- Support contact information

---

**Deployment completed successfully! 🎉**

Your Mobile Phone Repair Shop application is now ready for production use across your organization.
