using System;
using System.Windows;
using System.Windows.Controls;
using AccountingApp.Core;

namespace AccountingApp.UI.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for AccessoryDialog.xaml
    /// </summary>
    public partial class AccessoryDialog : Window
    {
        /// <summary>
        /// Gets the accessory being edited or created.
        /// </summary>
        public Accessory Accessory { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="AccessoryDialog"/> class for adding a new accessory.
        /// </summary>
        public AccessoryDialog()
        {
            InitializeComponent();
            Accessory = new Accessory();
            DialogTitle.Text = "Add Accessory";
            
            // Set default values
            AddedDatePicker.SelectedDate = DateTime.Now;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AccessoryDialog"/> class for editing an existing accessory.
        /// </summary>
        /// <param name="accessory">The accessory to edit.</param>
        public AccessoryDialog(Accessory accessory)
        {
            InitializeComponent();
            
            if (accessory == null)
                throw new ArgumentNullException(nameof(accessory));

            // Create a copy of the accessory to avoid modifying the original until save
            Accessory = new Accessory
            {
                Id = accessory.Id,
                Name = accessory.Name,
                Description = accessory.Description,
                Category = accessory.Category,
                Price = accessory.Price,
                StockQuantity = accessory.StockQuantity,
                AddedDate = accessory.AddedDate,
                LastUpdated = accessory.LastUpdated
            };

            DialogTitle.Text = "Edit Accessory";
            LoadAccessoryData();
        }

        /// <summary>
        /// Loads the accessory data into the form fields.
        /// </summary>
        private void LoadAccessoryData()
        {
            NameTextBox.Text = Accessory.Name;
            DescriptionTextBox.Text = Accessory.Description;
            
            // Set category combobox
            CategoryComboBox.Text = Accessory.Category;

            PriceTextBox.Text = Accessory.Price.ToString("F2");
            StockQuantityTextBox.Text = Accessory.StockQuantity.ToString();
            AddedDatePicker.SelectedDate = Accessory.AddedDate;
        }

        /// <summary>
        /// Validates the form data.
        /// </summary>
        /// <returns>True if the form is valid; otherwise, false.</returns>
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("Please enter an accessory name.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CategoryComboBox.Text))
            {
                MessageBox.Show("Please select or enter a category.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryComboBox.Focus();
                return false;
            }

            // Validate price
            if (!decimal.TryParse(PriceTextBox.Text, out decimal price) || price < 0)
            {
                MessageBox.Show("Please enter a valid price.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                PriceTextBox.Focus();
                return false;
            }

            // Validate stock quantity
            if (!int.TryParse(StockQuantityTextBox.Text, out int stockQuantity) || stockQuantity < 0)
            {
                MessageBox.Show("Please enter a valid stock quantity.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                StockQuantityTextBox.Focus();
                return false;
            }

            if (AddedDatePicker.SelectedDate == null)
            {
                MessageBox.Show("Please select an added date.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                AddedDatePicker.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Saves the form data to the accessory object.
        /// </summary>
        private void SaveFormData()
        {
            Accessory.Name = NameTextBox.Text.Trim();
            Accessory.Description = DescriptionTextBox.Text.Trim();
            Accessory.Category = CategoryComboBox.Text.Trim();
            Accessory.Price = decimal.Parse(PriceTextBox.Text);
            Accessory.StockQuantity = int.Parse(StockQuantityTextBox.Text);
            Accessory.AddedDate = AddedDatePicker.SelectedDate.Value;

            // Set last updated date for existing accessories
            if (Accessory.Id > 0)
            {
                Accessory.LastUpdated = DateTime.Now;
            }
        }

        /// <summary>
        /// Handles the click event for the Save button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                SaveFormData();
                DialogResult = true;
                Close();
            }
        }

        /// <summary>
        /// Handles the click event for the Cancel button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
