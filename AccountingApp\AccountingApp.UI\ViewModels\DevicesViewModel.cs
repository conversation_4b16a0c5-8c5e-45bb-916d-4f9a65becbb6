using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using AccountingApp.Core;
using AccountingApp.Data.Repositories;
using AccountingApp.UI.Commands;
using AccountingApp.UI.Views.Dialogs;

namespace AccountingApp.UI.ViewModels
{
    /// <summary>
    /// ViewModel for the Devices page.
    /// </summary>
    public class DevicesViewModel : BaseViewModel
    {
        private readonly Repository<Device> _deviceRepository;
        private Device _selectedDevice;

        /// <summary>
        /// Initializes a new instance of the <see cref="DevicesViewModel"/> class.
        /// </summary>
        public DevicesViewModel()
        {
            var context = new AppDbContext();
            _deviceRepository = new Repository<Device>(context);
            
            Devices = new ObservableCollection<Device>();
            
            // Initialize commands
            AddDeviceCommand = new RelayCommand(AddDevice);
            EditDeviceCommand = new RelayCommand(EditDevice, CanEditOrDeleteDevice);
            DeleteDeviceCommand = new RelayCommand(DeleteDevice, CanEditOrDeleteDevice);
            
            // Load devices
            LoadDevices();
        }

        /// <summary>
        /// Gets the collection of devices.
        /// </summary>
        public ObservableCollection<Device> Devices { get; }

        /// <summary>
        /// Gets or sets the selected device.
        /// </summary>
        public Device SelectedDevice
        {
            get => _selectedDevice;
            set => SetProperty(ref _selectedDevice, value);
        }

        /// <summary>
        /// Gets the command to add a new device.
        /// </summary>
        public ICommand AddDeviceCommand { get; }

        /// <summary>
        /// Gets the command to edit the selected device.
        /// </summary>
        public ICommand EditDeviceCommand { get; }

        /// <summary>
        /// Gets the command to delete the selected device.
        /// </summary>
        public ICommand DeleteDeviceCommand { get; }

        /// <summary>
        /// Loads all devices from the database.
        /// </summary>
        private void LoadDevices()
        {
            try
            {
                var devices = _deviceRepository.GetAll().OrderByDescending(d => d.ReceivedDate);
                Devices.Clear();
                foreach (var device in devices)
                {
                    Devices.Add(device);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading devices: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Adds a new device.
        /// </summary>
        private void AddDevice()
        {
            var dialog = new DeviceDialog();
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    var device = dialog.Device;
                    _deviceRepository.Add(device);
                    _deviceRepository.SaveChanges();
                    Devices.Insert(0, device); // Add to the beginning since we order by received date descending
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error adding device: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Edits the selected device.
        /// </summary>
        private void EditDevice()
        {
            if (SelectedDevice == null) return;

            var dialog = new DeviceDialog(SelectedDevice);
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    _deviceRepository.Update(dialog.Device);
                    _deviceRepository.SaveChanges();
                    LoadDevices(); // Refresh the list
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error updating device: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Deletes the selected device.
        /// </summary>
        private void DeleteDevice()
        {
            if (SelectedDevice == null) return;

            var result = MessageBox.Show($"Are you sure you want to delete device '{SelectedDevice.Brand} {SelectedDevice.Model}'?", 
                                       "Confirm Delete", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _deviceRepository.Remove(SelectedDevice);
                    _deviceRepository.SaveChanges();
                    Devices.Remove(SelectedDevice);
                    SelectedDevice = null;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting device: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Determines whether the edit or delete device commands can execute.
        /// </summary>
        /// <returns>True if a device is selected; otherwise, false.</returns>
        private bool CanEditOrDeleteDevice()
        {
            return SelectedDevice != null;
        }
    }
}
