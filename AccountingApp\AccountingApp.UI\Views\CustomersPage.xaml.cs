using System.Windows;
using System.Windows.Controls;
using AccountingApp.UI.ViewModels;

namespace AccountingApp.UI.Views
{
    /// <summary>
    /// Interaction logic for CustomersPage.xaml
    /// </summary>
    public partial class CustomersPage : Page
    {
        /// <summary>
        /// Gets the view model for this page.
        /// </summary>
        public CustomersViewModel ViewModel { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="CustomersPage"/> class.
        /// </summary>
        public CustomersPage()
        {
            InitializeComponent();
            ViewModel = new CustomersViewModel();
            DataContext = ViewModel;
        }

        /// <summary>
        /// Handles the click event for the Add Customer button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void AddCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.AddCustomerCommand.Execute(null);
        }

        /// <summary>
        /// Handles the click event for the Edit Customer button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void EditCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.EditCustomerCommand.Execute(null);
        }

        /// <summary>
        /// Handles the click event for the Delete Customer button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void DeleteCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.DeleteCustomerCommand.Execute(null);
        }
    }
}
