using System.Windows;
using System.Windows.Controls;
using AccountingApp.UI.ViewModels;

namespace AccountingApp.UI.Views
{
    /// <summary>
    /// Interaction logic for AccessoriesPage.xaml
    /// </summary>
    public partial class AccessoriesPage : Page
    {
        /// <summary>
        /// Gets the view model for this page.
        /// </summary>
        public AccessoriesViewModel ViewModel { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="AccessoriesPage"/> class.
        /// </summary>
        public AccessoriesPage()
        {
            InitializeComponent();
            ViewModel = new AccessoriesViewModel();
            DataContext = ViewModel;
        }

        /// <summary>
        /// Handles the click event for the Add Accessory button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void AddAccessoryButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.AddAccessoryCommand.Execute(null);
        }

        /// <summary>
        /// Handles the click event for the Edit Accessory button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void EditAccessoryButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.EditAccessoryCommand.Execute(null);
        }

        /// <summary>
        /// Handles the click event for the Delete Accessory button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void DeleteAccessoryButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.DeleteAccessoryCommand.Execute(null);
        }
    }
}
