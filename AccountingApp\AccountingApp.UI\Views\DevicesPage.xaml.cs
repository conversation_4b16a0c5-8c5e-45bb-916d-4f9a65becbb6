using System.Windows;
using System.Windows.Controls;
using AccountingApp.UI.ViewModels;

namespace AccountingApp.UI.Views
{
    /// <summary>
    /// Interaction logic for DevicesPage.xaml
    /// </summary>
    public partial class DevicesPage : Page
    {
        /// <summary>
        /// Gets the view model for this page.
        /// </summary>
        public DevicesViewModel ViewModel { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="DevicesPage"/> class.
        /// </summary>
        public DevicesPage()
        {
            InitializeComponent();
            ViewModel = new DevicesViewModel();
            DataContext = ViewModel;
        }

        /// <summary>
        /// Handles the click event for the Add Device button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void AddDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.AddDeviceCommand.Execute(null);
        }

        /// <summary>
        /// Handles the click event for the Edit Device button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void EditDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.EditDeviceCommand.Execute(null);
        }

        /// <summary>
        /// Handles the click event for the Delete Device button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void DeleteDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.DeleteDeviceCommand.Execute(null);
        }
    }
}
