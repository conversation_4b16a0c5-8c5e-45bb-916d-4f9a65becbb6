<Window x:Class="AccountingApp.UI.Views.Dialogs.CustomerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Customer Details" 
        Height="500" 
        Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Dialog Title -->
        <TextBlock Grid.Row="0" 
                   x:Name="DialogTitle"
                   Text="Add Customer" 
                   Style="{StaticResource PageTitleStyle}"
                   Margin="0,0,0,24"/>

        <!-- Customer Form -->
        <materialDesign:Card Grid.Row="1" 
                           Style="{StaticResource CardStyle}"
                           Margin="0">
            <StackPanel>
                <!-- Name Field -->
                <TextBox x:Name="NameTextBox"
                         materialDesign:HintAssist.Hint="Full Name"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"
                         MaxLength="100"/>

                <!-- Phone Number Field -->
                <TextBox x:Name="PhoneTextBox"
                         materialDesign:HintAssist.Hint="Phone Number"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"
                         MaxLength="20"/>

                <!-- Email Field -->
                <TextBox x:Name="EmailTextBox"
                         materialDesign:HintAssist.Hint="Email Address"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,0,0,16"
                         MaxLength="100"/>

                <!-- Address Field -->
                <TextBox x:Name="AddressTextBox"
                         materialDesign:HintAssist.Hint="Address"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinLines="3"
                         MaxLines="5"
                         VerticalScrollBarVisibility="Auto"
                         MaxLength="200"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right"
                    Margin="0,24,0,0">
            <Button x:Name="CancelButton"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Content="Cancel"
                    Click="CancelButton_Click"
                    Margin="0,0,8,0"
                    Width="80"/>

            <Button x:Name="SaveButton"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Content="Save"
                    Click="SaveButton_Click"
                    Width="80"/>
        </StackPanel>
    </Grid>
</Window>
