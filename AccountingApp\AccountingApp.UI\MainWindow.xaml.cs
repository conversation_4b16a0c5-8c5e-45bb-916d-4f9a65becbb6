using System.Windows;
using System.Windows.Controls;
using AccountingApp.UI.Views;

namespace AccountingApp.UI
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MainWindow"/> class.
        /// </summary>
        public MainWindow()
        {
            InitializeComponent();
            
            // Navigate to the Customers page by default
            NavigateToPage("Customers");
        }

        /// <summary>
        /// Handles the click event for navigation buttons.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void NavigationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string pageName)
            {
                NavigateToPage(pageName);
                UpdateNavigationButtonStyles(button);
            }
        }

        /// <summary>
        /// Navigates to the specified page.
        /// </summary>
        /// <param name="pageName">The name of the page to navigate to.</param>
        private void NavigateToPage(string pageName)
        {
            Page page = null;

            switch (pageName)
            {
                case "Customers":
                    page = new CustomersPage();
                    break;
                case "Devices":
                    page = new DevicesPage();
                    break;
                case "Accessories":
                    page = new AccessoriesPage();
                    break;
                case "Invoices":
                    page = new InvoicesPage();
                    break;
                default:
                    page = new CustomersPage(); // Default fallback
                    break;
            }

            if (page != null)
            {
                MainFrame.Navigate(page);
            }
        }

        /// <summary>
        /// Updates the visual styles of navigation buttons to highlight the active one.
        /// </summary>
        /// <param name="activeButton">The currently active button.</param>
        private void UpdateNavigationButtonStyles(Button activeButton)
        {
            // Reset all navigation buttons to default style
            CustomersButton.Style = (Style)FindResource("NavigationButtonStyle");
            DevicesButton.Style = (Style)FindResource("NavigationButtonStyle");
            AccessoriesButton.Style = (Style)FindResource("NavigationButtonStyle");
            InvoicesButton.Style = (Style)FindResource("NavigationButtonStyle");

            // Highlight the active button (you can create a custom style for this)
            // For now, we'll just use the same style but you can create an "ActiveNavigationButtonStyle"
            activeButton.Style = (Style)FindResource("NavigationButtonStyle");
        }
    }
}
