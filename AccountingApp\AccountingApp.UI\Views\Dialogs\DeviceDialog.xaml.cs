using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using AccountingApp.Core;

namespace AccountingApp.UI.Views.Dialogs
{
    /// <summary>
    /// Interaction logic for DeviceDialog.xaml
    /// </summary>
    public partial class DeviceDialog : Window
    {
        /// <summary>
        /// Gets the device being edited or created.
        /// </summary>
        public Device Device { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceDialog"/> class for adding a new device.
        /// </summary>
        public DeviceDialog()
        {
            InitializeComponent();
            Device = new Device();
            DialogTitle.Text = "Add Device";
            
            // Set default values
            StatusComboBox.SelectedIndex = 0; // "Received"
            ReceivedDatePicker.SelectedDate = DateTime.Now;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceDialog"/> class for editing an existing device.
        /// </summary>
        /// <param name="device">The device to edit.</param>
        public DeviceDialog(Device device)
        {
            InitializeComponent();
            
            if (device == null)
                throw new ArgumentNullException(nameof(device));

            // Create a copy of the device to avoid modifying the original until save
            Device = new Device
            {
                Id = device.Id,
                Brand = device.Brand,
                Model = device.Model,
                SerialNumber = device.SerialNumber,
                Problem = device.Problem,
                Status = device.Status,
                ReceivedDate = device.ReceivedDate,
                EstimatedCost = device.EstimatedCost,
                ActualCost = device.ActualCost
            };

            DialogTitle.Text = "Edit Device";
            LoadDeviceData();
        }

        /// <summary>
        /// Loads the device data into the form fields.
        /// </summary>
        private void LoadDeviceData()
        {
            BrandTextBox.Text = Device.Brand;
            ModelTextBox.Text = Device.Model;
            SerialNumberTextBox.Text = Device.SerialNumber;
            ProblemTextBox.Text = Device.Problem;
            
            // Set status combobox
            foreach (ComboBoxItem item in StatusComboBox.Items)
            {
                if (item.Content.ToString() == Device.Status)
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }

            EstimatedCostTextBox.Text = Device.EstimatedCost.ToString("F2");
            ActualCostTextBox.Text = Device.ActualCost.ToString("F2");
            ReceivedDatePicker.SelectedDate = Device.ReceivedDate;
        }

        /// <summary>
        /// Validates the form data.
        /// </summary>
        /// <returns>True if the form is valid; otherwise, false.</returns>
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(BrandTextBox.Text))
            {
                MessageBox.Show("Please enter a device brand.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                BrandTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ModelTextBox.Text))
            {
                MessageBox.Show("Please enter a device model.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                ModelTextBox.Focus();
                return false;
            }

            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("Please select a status.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusComboBox.Focus();
                return false;
            }

            // Validate estimated cost
            if (!string.IsNullOrWhiteSpace(EstimatedCostTextBox.Text))
            {
                if (!decimal.TryParse(EstimatedCostTextBox.Text, out decimal estimatedCost) || estimatedCost < 0)
                {
                    MessageBox.Show("Please enter a valid estimated cost.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    EstimatedCostTextBox.Focus();
                    return false;
                }
            }

            // Validate actual cost
            if (!string.IsNullOrWhiteSpace(ActualCostTextBox.Text))
            {
                if (!decimal.TryParse(ActualCostTextBox.Text, out decimal actualCost) || actualCost < 0)
                {
                    MessageBox.Show("Please enter a valid actual cost.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ActualCostTextBox.Focus();
                    return false;
                }
            }

            if (ReceivedDatePicker.SelectedDate == null)
            {
                MessageBox.Show("Please select a received date.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                ReceivedDatePicker.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Saves the form data to the device object.
        /// </summary>
        private void SaveFormData()
        {
            Device.Brand = BrandTextBox.Text.Trim();
            Device.Model = ModelTextBox.Text.Trim();
            Device.SerialNumber = SerialNumberTextBox.Text.Trim();
            Device.Problem = ProblemTextBox.Text.Trim();
            Device.Status = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
            Device.ReceivedDate = ReceivedDatePicker.SelectedDate.Value;

            // Parse costs
            if (decimal.TryParse(EstimatedCostTextBox.Text, out decimal estimatedCost))
                Device.EstimatedCost = estimatedCost;
            else
                Device.EstimatedCost = 0;

            if (decimal.TryParse(ActualCostTextBox.Text, out decimal actualCost))
                Device.ActualCost = actualCost;
            else
                Device.ActualCost = 0;
        }

        /// <summary>
        /// Handles the click event for the Save button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateForm())
            {
                SaveFormData();
                DialogResult = true;
                Close();
            }
        }

        /// <summary>
        /// Handles the click event for the Cancel button.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">The <see cref="RoutedEventArgs"/> instance containing the event data.</param>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
